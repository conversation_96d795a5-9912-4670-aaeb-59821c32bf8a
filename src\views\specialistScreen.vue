<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <!-- 页面指示器 -->
    <!-- <div
      v-if="scheduleData.length > itemsPerPage"
      style="color: white; text-align: center; margin-bottom: 10px"
    >
      <span class="page-info">
        第 {{ getCurrentPageInfo().currentPage }} 页 / 共 {{ getCurrentPageInfo().totalPages }} 页
        (显示第 {{ getCurrentPageInfo().startIndex }}-{{ getCurrentPageInfo().endIndex }} 条， 共
        {{ getCurrentPageInfo().totalRecords }} 条记录)
      </span>
    </div> -->

    <div class="page-content-container">
      <!-- 专家排班表格 -->
      <div class="schedule-container">
        <!-- 横幅标题 -->
        <table class="schedule-table">
          <tbody>
            <tr>
              <td colspan="15" class="banner-title">门诊医生出诊一览表</td>
            </tr>
            <!-- 10行数据 -->
            <tr v-for="rowIndex in 10" :key="`row-${rowIndex}`">
              <!-- 每行5个人，每人3列 -->
              <template v-for="personIndex in 5">
                <td :key="`dept-${rowIndex}-${personIndex}`" class="department-cell">
                  {{ getPersonData(rowIndex, personIndex, "DepartmentName") }}
                </td>
                <td :key="`doctor-${rowIndex}-${personIndex}`" class="doctor-cell">
                  {{ getPersonData(rowIndex, personIndex, "DoctorName") }}
                </td>
                <td :key="`location-${rowIndex}-${personIndex}`" class="location-cell">
                  {{ getPersonData(rowIndex, personIndex, "AdmitAddress") }}
                </td>
              </template>
            </tr>
            <tr>
              <td colspan="3" class="tips-1">请实名制就诊</td>
              <td colspan="12" class="tips-2">待补充</td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 医生信息 -->
      <div class="doctor-info"></div>
    </div>
  </div>
</template>

<script>
import { dataConversion } from "@/utils/index.js";
import scheduleTestData from "./scheduleTestData.js";

export default {
  name: "specialistScreen",
  data() {
    return {
      // 科室列表
      departmentList: [],
      // 排班数据
      scheduleData: [],
      // 当前页码（从0开始）
      currentPage: 0,
      // 轮播定时器
      rotationTimer: null,
      // 每页显示的数据条数
      itemsPerPage: 50,
    };
  },
  created() {
    this.getDepartmentList();
    this.getScheduleData();
  },
  mounted() {
    // 如果数据超过50条，启动轮播
    this.startRotation();
    // 输出调试信息
    console.log("页面挂载完成，数据总数:", this.scheduleData.length);
    console.log("每页显示:", this.itemsPerPage, "条");
    console.log("总页数:", Math.ceil(this.scheduleData.length / this.itemsPerPage));
  },
  beforeDestroy() {
    // 清理定时器
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
    }
  },
  methods: {
    // 获取科室列表
    async getDepartmentList() {
      const res = await this.$api.department.queryDepartment();
      console.log("二级科室列表", res);
      if (res.success) {
        console.log("请求成功");
        const department = res.data.Response?.Departments?.Department || [];
        this.departmentList = dataConversion(department);
      } else {
        this.$message.error(res.message);
      }
    },
    // 获取排班数据
    async getScheduleData() {
      let filteredData = scheduleTestData.filter(t => {
        return t.ScheduleStatus === "N";
      });

      // 为了演示翻页功能，复制数据创建更多记录
      // 在实际应用中，这里应该是从API获取的真实数据
      if (filteredData.length <= 50) {
        const originalData = [...filteredData];
        // 复制数据3次，创建约150条记录用于演示翻页
        for (let i = 1; i <= 2; i++) {
          const duplicatedData = originalData.map((item, index) => ({
            ...item,
            ScheduleItemCode: `${item.ScheduleItemCode}_copy${i}_${index}`,
            DoctorName: `${item.DoctorName}(${i + 1})`,
            DepartmentName: `${item.DepartmentName}(${i + 1})`,
            AdmitAddress: `${item.AdmitAddress}(${i + 1})`,
          }));
          filteredData = [...filteredData, ...duplicatedData];
        }
      }

      this.scheduleData = filteredData;
      console.log(`加载了 ${this.scheduleData.length} 条排班数据`);
    },
    // 获取指定位置的人员数据
    getPersonData(rowIndex, personIndex, field) {
      // 计算在当前页表格中的索引位置 (行索引-1) * 5 + (人员索引-1)
      const tableIndex = (rowIndex - 1) * 5 + (personIndex - 1);
      // 计算在整个数据集中的实际索引：当前页起始位置 + 表格内位置
      const dataIndex = this.currentPage * this.itemsPerPage + tableIndex;

      if (dataIndex < this.scheduleData.length && this.scheduleData[dataIndex]) {
        return this.scheduleData[dataIndex][field] || "";
      }
      // 返回空字符串，让CSS百分比高度来控制行高度一致性
      return "";
    },
    // 启动数据轮播
    startRotation() {
      // 轮播时间
      const ROTATION_TIME = 30 * 1000;

      // 只有当数据超过一页（50条）时才启动轮播
      if (this.scheduleData.length > this.itemsPerPage) {
        this.rotationTimer = setInterval(() => {
          // 计算总页数
          const totalPages = Math.ceil(this.scheduleData.length / this.itemsPerPage);
          // 翻到下一页，如果是最后一页则回到第一页
          const oldPage = this.currentPage;
          this.currentPage = (this.currentPage + 1) % totalPages;
          console.log(`页面切换: ${oldPage + 1} → ${this.currentPage + 1} (共${totalPages}页)`);
        }, ROTATION_TIME);
      }
    },
    // 获取当前页信息（用于调试或显示）
    getCurrentPageInfo() {
      const totalPages = Math.ceil(this.scheduleData.length / this.itemsPerPage);
      const startIndex = this.currentPage * this.itemsPerPage;
      const endIndex = Math.min(startIndex + this.itemsPerPage, this.scheduleData.length);
      return {
        currentPage: this.currentPage + 1, // 显示时从1开始
        totalPages,
        startIndex: startIndex + 1, // 显示时从1开始
        endIndex,
        totalRecords: this.scheduleData.length,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
$leftWidth: 75%;
$rightWidth: 25%;

$text-color-red: #ff0000;
$text-color-yellow: #ffff00;
$text-color-red-thin: #ff9999;
$text-color-red-green: #39FF14;
$border-color: #666666;

$table-cell-padding: 5px;
$title-size:22px;

.page-container {
  background-color: #000000;
  padding: 0;
  .page-content-container {
    display: flex;
    height: 100%;
    .schedule-container {
      height: 100%;
      width: $leftWidth;
      display: flex;
      flex-direction: column;

      .schedule-table {
        width: 100%;
        height: 100%;
        border-collapse: collapse;
        font-size: 14px;
        // table-layout: fixed; // 固定表格布局，确保列宽一致

        tbody {
          height: 100%;
          .banner-title {
            color: $text-color-red;
            text-align: center;
            font-size: $title-size;
            font-weight: bold;
            letter-spacing: 40px;
          }
          .tips-1 {
            font-size: $title-size;
            text-align: center;
            color: $text-color-red;
          }
          .tips-2 {
            text-align: left;
            color: $text-color-red-green;
          }
          th,
          td {
            border: 1px solid $border-color;
            padding: $table-cell-padding;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
            max-width: 120px;
          }

          tr {
            background-color: transparent; // 去掉行背景色
            height: calc(100% / 12);
          }

          .department-cell {
            color: $text-color-red;
          }

          .doctor-cell {
            color: $text-color-yellow;
          }

          .location-cell {
            color: $text-color-red-thin;
          }
        }
      }
    }
    .doctor-info {
      width: $rightWidth;
      background-color: #fff;
    }
  }
}
</style>
